<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File-Man</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="title-bar">
                <h1>📁 File-Man</h1>
                <div class="window-controls">
                    <button class="control-btn minimize" onclick="window.electronAPI?.minimize?.()">−</button>
                    <button class="control-btn close" onclick="window.electronAPI?.close?.()">×</button>
                </div>
            </div>
            <p>Organize your files by type and date, then verify with MD5 hash checking</p>
        </header>

        <main>
            <!-- Folder Selection Section -->
            <section class="folder-selection">
                <div class="folder-group">
                    <div class="folder-item">
                        <label>Source Folder:</label>
                        <div class="folder-input-group">
                            <input type="text" id="baseFolderPath" placeholder="Paste or select source folder path...">
                            <button id="selectBaseFolder" class="btn btn-secondary">Browse</button>
                        </div>
                        <div class="folder-stats" id="baseStats"></div>
                    </div>

                    <div class="folder-item">
                        <label>Destination Folder:</label>
                        <div class="folder-input-group">
                            <input type="text" id="destFolderPath" placeholder="Paste or select destination folder path...">
                            <button id="selectDestFolder" class="btn btn-secondary">Browse</button>
                        </div>
                        <div class="folder-stats" id="destStats"></div>
                    </div>
                </div>
            </section>

            <!-- Options Section -->
            <section class="options-section">
                <h3>Copy Options</h3>
                <div class="options-grid">
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableDuplicateCheck" checked>
                            <span class="checkmark"></span>
                            Enable Smart Duplicate Detection (MD5)
                        </label>
                        <div class="sub-options" id="duplicateOptions">
                            <label class="radio-label">
                                <input type="radio" name="duplicateAction" value="rename" checked>
                                <span class="radio-mark"></span>
                                Rename duplicate files
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="duplicateAction" value="skip">
                                <span class="radio-mark"></span>
                                Don't copy duplicate files
                            </label>
                        </div>
                    </div>
                    <label class="checkbox-label">
                        <input type="checkbox" id="createLogs" checked>
                        <span class="checkmark"></span>
                        Generate Detailed Logs
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="preserveStructure" checked>
                        <span class="checkmark"></span>
                        Preserve Structure for 'Others'
                    </label>
                </div>
            </section>

            <!-- Action Buttons Section -->
            <section class="action-section">
                <button id="copyButton" class="btn btn-primary" disabled>
                    <span class="btn-icon">📋</span>
                    Copy & Organize Files
                </button>
                <button id="stopButton" class="btn btn-danger" disabled style="display: none;">
                    <span class="btn-icon">⏹️</span>
                    Stop Copy
                </button>
                <button id="verifyButton" class="btn btn-success" disabled>
                    <span class="btn-icon">✅</span>
                    Verify Files (MD5)
                </button>
            </section>

            <!-- Progress Section -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-header">
                    <h3 id="progressTitle">Processing...</h3>
                    <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-details">
                    <div id="progressStatus">Initializing...</div>
                    <div id="progressStats"></div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <h3>Operation Results</h3>
                <div id="resultsContent"></div>
            </section>

            <!-- Log Section -->
            <section class="log-section">
                <h3>Activity Log</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry">Application started. Ready to organize files.</div>
                </div>
            </section>
        </main>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
