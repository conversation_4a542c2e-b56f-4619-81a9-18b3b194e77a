const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');

let mainWindow;
let currentFileManager = null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'), // Optional: Add app icon
    title: 'File-Man',
    frame: false, // Remove title bar
    titleBarStyle: 'hidden', // Hide title bar on macOS
    autoHideMenuBar: true, // Hide menu bar
    menuBarVisible: false // Ensure menu bar is hidden
  });

  mainWindow.loadFile('renderer/index.html');

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers
ipcMain.handle('select-folder', async (event, title = 'Select Folder') => {
  const result = await dialog.showOpenDialog(mainWindow, {
    title,
    properties: ['openDirectory']
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('copy-files', async (event, basePath, destPath, options = {}) => {
  const { FileManager } = require('./src/fileManager');
  currentFileManager = new FileManager();

  return await currentFileManager.copyFiles(basePath, destPath, options, (progress) => {
    mainWindow.webContents.send('copy-progress', progress);
  });
});

ipcMain.handle('stop-copy', async (event) => {
  if (currentFileManager) {
    currentFileManager.stopCopy();
    return { success: true, message: 'Copy operation stopped' };
  }
  return { success: false, message: 'No active copy operation' };
});

ipcMain.handle('verify-files', async (event, basePath, destPath) => {
  const { HashVerifier } = require('./src/hashVerifier');
  const verifier = new HashVerifier();

  return await verifier.verifyFiles(basePath, destPath, (progress) => {
    mainWindow.webContents.send('verify-progress', progress);
  });
});

ipcMain.handle('get-folder-stats', async (event, folderPath) => {
  try {
    const { getDirectoryStats } = require('./src/utils');
    return await getDirectoryStats(folderPath);
  } catch (error) {
    console.error('Error getting folder stats:', error);
    return { totalFiles: 0, totalSize: 0 };
  }
});

// Window control handlers
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
