const fs = require('fs').promises;
const crypto = require('crypto');
const path = require('path');
const {
    getAllFiles,
    calculateMD5,
    createLogEntry,
    formatFileSize
} = require('./utils');

class HashVerifier {
    constructor() {
        this.logEntries = [];
        this.stats = {
            totalFiles: 0,
            verifiedFiles: 0,
            mismatchedFiles: 0,
            missingFiles: 0,
            errors: []
        };
    }

    /**
     * Main method to verify files using MD5 hash
     */
    async verifyFiles(basePath, destPath, progressCallback = null) {
        try {
            this.logEntries = [];
            this.stats = {
                totalFiles: 0,
                verifiedFiles: 0,
                mismatchedFiles: 0,
                missingFiles: 0,
                errors: []
            };

            this.addLog('Starting MD5 hash verification process');
            this.addLog(`Source: ${basePath}`);
            this.addLog(`Destination: ${destPath}`);

            // Get all files from base directory
            const sourceFiles = await getAllFiles(basePath);
            this.stats.totalFiles = sourceFiles.length;

            this.addLog(`Found ${sourceFiles.length} files to verify`);

            if (progressCallback) {
                progressCallback({
                    percentage: 0,
                    status: `Found ${sourceFiles.length} files to verify`,
                    stats: { processed: 0, total: sourceFiles.length }
                });
            }

            // Create file mapping for verification
            const fileMapping = await this.createFileMapping(basePath, destPath);

            // Verify files concurrently for better performance
            const concurrency = Math.min(Math.max(2, Math.floor(require('os').cpus().length / 2)), 8);
            this.addLog(`Using ${concurrency} concurrent workers for verification`);
            await this.verifyFilesConcurrently(sourceFiles, fileMapping, basePath, destPath, progressCallback, concurrency);

            // Save verification log
            const logFile = await this.saveVerificationLog(destPath);

            this.addLog('MD5 hash verification completed');
            this.addLog(`Total files: ${this.stats.totalFiles}`);
            this.addLog(`Verified files: ${this.stats.verifiedFiles}`);
            this.addLog(`Mismatched files: ${this.stats.mismatchedFiles}`);
            this.addLog(`Missing files: ${this.stats.missingFiles}`);
            this.addLog(`Errors: ${this.stats.errors.length}`);

            const allFilesMatch = this.stats.mismatchedFiles === 0 && this.stats.missingFiles === 0;

            return {
                success: true,
                totalFiles: this.stats.totalFiles,
                verifiedFiles: this.stats.verifiedFiles,
                mismatchedFiles: this.stats.mismatchedFiles,
                missingFiles: this.stats.missingFiles,
                allFilesMatch,
                errors: this.stats.errors,
                logFile
            };

        } catch (error) {
            this.addLog(`Fatal error during verification: ${error.message}`, 'error');
            return {
                success: false,
                error: error.message,
                totalFiles: this.stats.totalFiles,
                verifiedFiles: this.stats.verifiedFiles,
                errors: this.stats.errors
            };
        }
    }

    /**
     * Verify multiple files concurrently for better performance
     */
    async verifyFilesConcurrently(sourceFiles, fileMapping, basePath, destPath, progressCallback, concurrency = 4) {
        let processedCount = 0;
        const totalFiles = sourceFiles.length;

        // Process files in batches with proper concurrency control
        const processBatch = async (batch) => {
            const promises = batch.map(async (sourceFile) => {
                try {
                    await this.verifyFile(sourceFile, fileMapping, basePath, destPath);
                    processedCount++;

                    const percentage = Math.round((processedCount / totalFiles) * 100);
                    if (progressCallback) {
                        progressCallback({
                            percentage,
                            status: `Verifying file ${processedCount} of ${totalFiles}`,
                            currentFile: path.basename(sourceFile),
                            stats: { processed: processedCount, total: totalFiles }
                        });
                    }
                } catch (error) {
                    this.stats.errors.push(`Error verifying ${sourceFile}: ${error.message}`);
                    this.addLog(`Error verifying ${sourceFile}: ${error.message}`, 'error');
                    processedCount++;
                }
            });

            await Promise.all(promises);
        };

        // Process files in batches
        for (let i = 0; i < sourceFiles.length; i += concurrency) {
            const batch = sourceFiles.slice(i, i + concurrency);
            await processBatch(batch);
        }
    }

    /**
     * Create mapping of source files to destination files
     * Only maps files that originated from the source directory
     */
    async createFileMapping(basePath, destPath) {
        const mapping = new Map();
        const sourceFiles = await getAllFiles(basePath);

        // Get all files from destination
        const destFiles = await getAllFiles(destPath);

        // Create a set of source filenames for filtering
        const sourceFileNames = new Set(sourceFiles.map(file => path.basename(file)));

        // Create mapping based on filename, but only for files that exist in source
        for (const destFile of destFiles) {
            const fileName = path.basename(destFile);

            // Handle renamed duplicates (remove _1, _2, etc.)
            const originalName = fileName.replace(/_\d+(\.[^.]+)?$/, '$1');

            // Only include files that exist in the source directory
            if (sourceFileNames.has(originalName)) {
                if (!mapping.has(originalName)) {
                    mapping.set(originalName, []);
                }
                mapping.get(originalName).push(destFile);
            }
        }

        return mapping;
    }

    /**
     * Verify individual file
     */
    async verifyFile(sourceFile, fileMapping, basePath, destPath) {
        try {
            const fileName = path.basename(sourceFile);
            const possibleDestFiles = fileMapping.get(fileName) || [];

            if (possibleDestFiles.length === 0) {
                this.stats.missingFiles++;
                this.addLog(`Missing file: ${fileName}`, 'warning');
                return;
            }

            // Calculate source file hash using optimized function
            const sourceHash = await calculateMD5(sourceFile);
            const sourceStats = await fs.stat(sourceFile);

            let verified = false;

            // Check each possible destination file
            for (const destFile of possibleDestFiles) {
                try {
                    const destHash = await calculateMD5(destFile);

                    if (sourceHash === destHash) {
                        this.stats.verifiedFiles++;
                        this.addLog(`✓ Verified: ${fileName} (${formatFileSize(sourceStats.size)})`);
                        verified = true;
                        break;
                    }
                } catch (error) {
                    this.addLog(`Error reading destination file ${destFile}: ${error.message}`, 'error');
                }
            }

            if (!verified) {
                this.stats.mismatchedFiles++;
                this.addLog(`✗ Hash mismatch: ${fileName}`, 'error');
            }

        } catch (error) {
            throw new Error(`Failed to verify file ${sourceFile}: ${error.message}`);
        }
    }

    // Note: calculateMD5 is now imported from utils.js for better performance and caching

    /**
     * Save verification log file
     */
    async saveVerificationLog(destPath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const logFileName = `verification-log-${timestamp}.txt`;
            const logFilePath = path.join(destPath, 'logs', logFileName);

            // Ensure logs directory exists
            const logsDir = path.dirname(logFilePath);
            try {
                await fs.access(logsDir);
            } catch (error) {
                await fs.mkdir(logsDir, { recursive: true });
            }

            // Create detailed verification report
            const report = [
                '='.repeat(60),
                'FILE VERIFICATION REPORT',
                '='.repeat(60),
                '',
                `Verification Date: ${new Date().toISOString()}`,
                `Total Files: ${this.stats.totalFiles}`,
                `Verified Files: ${this.stats.verifiedFiles}`,
                `Mismatched Files: ${this.stats.mismatchedFiles}`,
                `Missing Files: ${this.stats.missingFiles}`,
                `Errors: ${this.stats.errors.length}`,
                '',
                'DETAILED LOG:',
                '-'.repeat(40),
                ...this.logEntries,
                '',
                'END OF REPORT',
                '='.repeat(60)
            ].join('\n');

            await fs.writeFile(logFilePath, report, 'utf8');

            this.addLog(`Verification log saved: ${logFilePath}`);
            return logFilePath;
        } catch (error) {
            this.addLog(`Error saving verification log: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * Add log entry
     */
    addLog(message, type = 'info') {
        const logEntry = createLogEntry(message, type);
        this.logEntries.push(logEntry);
        console.log(logEntry);
    }
}

module.exports = { HashVerifier };
