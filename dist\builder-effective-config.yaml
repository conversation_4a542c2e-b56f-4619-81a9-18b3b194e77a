directories:
  output: dist
  buildResources: build
appId: com.fileorganizer.verifier
productName: File-Man
files:
  - filter:
      - main.js
      - preload.js
      - renderer/**/*
      - src/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  forceCodeSigning: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: File-Man
portable:
  artifactName: File-Man-${version}-portable.exe
forceCodeSigning: false
electronVersion: 36.3.1
