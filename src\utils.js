const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// File type mappings
const FILE_TYPES = {
    images: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg', '.ico', '.raw', '.cr2', '.nef', '.arw'],
    videos: ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.vob'],
    documents: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.odt', '.ods', '.odp', '.csv'],
    audio: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus', '.aiff', '.au', '.ra', '.amr', '.3ga']
};

/**
 * Determine file category based on extension
 */
function getFileCategory(filePath) {
    const ext = path.extname(filePath).toLowerCase();

    if (FILE_TYPES.images.includes(ext)) return 'Image';
    if (FILE_TYPES.videos.includes(ext)) return 'Videos';
    if (FILE_TYPES.documents.includes(ext)) return 'Documents';
    if (FILE_TYPES.audio.includes(ext)) return 'Audio';
    return 'Others';
}

/**
 * Get file creation date and format for folder structure
 */
async function getFileDateInfo(filePath) {
    try {
        const stats = await fs.stat(filePath);
        const creationDate = stats.birthtime;

        return {
            year: creationDate.getFullYear().toString(),
            month: String(creationDate.getMonth() + 1).padStart(2, '0'),
            monthName: creationDate.toLocaleString('default', { month: 'long' }),
            fullDate: creationDate
        };
    } catch (error) {
        console.error(`Error getting date info for ${filePath}:`, error);
        const now = new Date();
        return {
            year: now.getFullYear().toString(),
            month: String(now.getMonth() + 1).padStart(2, '0'),
            monthName: now.toLocaleString('default', { month: 'long' }),
            fullDate: now
        };
    }
}

/**
 * Create destination path based on file category and date
 */
async function createDestinationPath(basePath, filePath, destRoot) {
    const category = getFileCategory(filePath);
    const dateInfo = await getFileDateInfo(filePath);

    if (category === 'Others') {
        // Preserve original directory structure for 'Others'
        const relativePath = path.relative(basePath, path.dirname(filePath));
        return path.join(destRoot, 'Others', relativePath);
    } else {
        // Create year/month structure for categorized files
        return path.join(destRoot, category, dateInfo.year, dateInfo.monthName);
    }
}

/**
 * Ensure directory exists, create if it doesn't
 */
async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
    } catch (error) {
        await fs.mkdir(dirPath, { recursive: true });
    }
}

/**
 * Get all files recursively from a directory
 */
async function getAllFiles(dirPath, fileList = []) {
    try {
        const items = await fs.readdir(dirPath);

        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stats = await fs.stat(fullPath);

            if (stats.isDirectory()) {
                await getAllFiles(fullPath, fileList);
            } else if (stats.isFile()) {
                fileList.push(fullPath);
            }
        }
    } catch (error) {
        console.error(`Error reading directory ${dirPath}:`, error);
    }

    return fileList;
}

/**
 * Get directory statistics (file count and total size)
 */
async function getDirectoryStats(dirPath) {
    try {
        const files = await getAllFiles(dirPath);
        let totalSize = 0;

        for (const file of files) {
            try {
                const stats = await fs.stat(file);
                totalSize += stats.size;
            } catch (error) {
                console.error(`Error getting stats for ${file}:`, error);
            }
        }

        return {
            totalFiles: files.length,
            totalSize: totalSize
        };
    } catch (error) {
        console.error(`Error getting directory stats for ${dirPath}:`, error);
        return { totalFiles: 0, totalSize: 0 };
    }
}

/**
 * Generate unique filename if file already exists
 */
async function generateUniqueFilename(filePath) {
    let counter = 1;
    const dir = path.dirname(filePath);
    const ext = path.extname(filePath);
    const baseName = path.basename(filePath, ext);

    let newPath = filePath;

    while (true) {
        try {
            await fs.access(newPath);
            // File exists, generate new name
            newPath = path.join(dir, `${baseName}_${counter}${ext}`);
            counter++;
        } catch (error) {
            // File doesn't exist, we can use this name
            break;
        }
    }

    return newPath;
}

/**
 * Format file size in human readable format
 */
function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Hash cache to avoid recalculating MD5 for the same files
const hashCache = new Map();

/**
 * Calculate MD5 hash of a file with caching
 */
async function calculateMD5(filePath) {
    try {
        // Get file stats for cache key
        const stats = await fs.stat(filePath);
        const cacheKey = `${filePath}:${stats.size}:${stats.mtime.getTime()}`;

        // Check cache first
        if (hashCache.has(cacheKey)) {
            return hashCache.get(cacheKey);
        }

        const hash = await new Promise((resolve, reject) => {
            const hasher = crypto.createHash('md5');
            const stream = require('fs').createReadStream(filePath, { highWaterMark: 64 * 1024 }); // 64KB chunks

            stream.on('data', (data) => {
                hasher.update(data);
            });

            stream.on('end', () => {
                resolve(hasher.digest('hex'));
            });

            stream.on('error', (error) => {
                reject(error);
            });
        });

        // Cache the result
        hashCache.set(cacheKey, hash);

        // Limit cache size to prevent memory issues
        if (hashCache.size > 10000) {
            const firstKey = hashCache.keys().next().value;
            hashCache.delete(firstKey);
        }

        return hash;
    } catch (error) {
        throw new Error(`Failed to calculate MD5 for ${filePath}: ${error.message}`);
    }
}

// Global hash index for fast duplicate detection
let destinationHashIndex = null;

/**
 * Build hash index for destination directory (called once per copy operation)
 */
async function buildDestinationHashIndex(destDir) {
    const hashIndex = new Map();
    const destFiles = await getAllFiles(destDir);

    console.log(`Building hash index for ${destFiles.length} files in destination...`);

    // Process files in batches to avoid overwhelming the system
    const batchSize = 50;
    for (let i = 0; i < destFiles.length; i += batchSize) {
        const batch = destFiles.slice(i, i + batchSize);

        await Promise.all(batch.map(async (destFile) => {
            try {
                // Quick size check first
                const stats = await fs.stat(destFile);
                const sizeKey = stats.size;

                if (!hashIndex.has(sizeKey)) {
                    hashIndex.set(sizeKey, []);
                }

                hashIndex.get(sizeKey).push({
                    path: destFile,
                    size: stats.size,
                    mtime: stats.mtime.getTime()
                });
            } catch (error) {
                console.warn(`Could not process ${destFile}: ${error.message}`);
            }
        }));
    }

    console.log(`Hash index built with ${hashIndex.size} size groups`);
    return hashIndex;
}

/**
 * Find files with same MD5 hash in destination directory (optimized)
 */
async function findDuplicatesByHash(sourceFilePath, destDir, useGlobalIndex = true) {
    try {
        // Get source file stats first for quick size comparison
        const sourceStats = await fs.stat(sourceFilePath);
        const sourceSize = sourceStats.size;

        // Build or use existing hash index
        if (useGlobalIndex && !destinationHashIndex) {
            destinationHashIndex = await buildDestinationHashIndex(destDir);
        }

        const duplicates = [];

        if (useGlobalIndex && destinationHashIndex) {
            // Use optimized index-based search
            const sameSize = destinationHashIndex.get(sourceSize) || [];

            if (sameSize.length > 0) {
                // Only calculate source hash if there are files with same size
                const sourceHash = await calculateMD5(sourceFilePath);

                // Check only files with same size
                for (const fileInfo of sameSize) {
                    try {
                        const destHash = await calculateMD5(fileInfo.path);
                        if (sourceHash === destHash) {
                            duplicates.push(fileInfo.path);
                        }
                    } catch (error) {
                        console.warn(`Could not calculate hash for ${fileInfo.path}: ${error.message}`);
                    }
                }

                return { sourceHash, duplicates };
            } else {
                // No files with same size, calculate hash anyway for return value
                const sourceHash = await calculateMD5(sourceFilePath);
                return { sourceHash, duplicates: [] };
            }
        } else {
            // Fallback to original method
            const sourceHash = await calculateMD5(sourceFilePath);
            const destFiles = await getAllFiles(destDir);

            for (const destFile of destFiles) {
                try {
                    // Quick size check first
                    const destStats = await fs.stat(destFile);
                    if (destStats.size !== sourceSize) {
                        continue; // Skip files with different sizes
                    }

                    const destHash = await calculateMD5(destFile);
                    if (sourceHash === destHash) {
                        duplicates.push(destFile);
                    }
                } catch (error) {
                    console.warn(`Could not calculate hash for ${destFile}: ${error.message}`);
                }
            }

            return { sourceHash, duplicates };
        }
    } catch (error) {
        throw new Error(`Failed to find duplicates for ${sourceFilePath}: ${error.message}`);
    }
}

/**
 * Clear the destination hash index (call when starting new copy operation)
 */
function clearDestinationHashIndex() {
    destinationHashIndex = null;
}

/**
 * Create log entry with timestamp
 */
function createLogEntry(message, type = 'info') {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] [${type.toUpperCase()}] ${message}`;
}

module.exports = {
    getFileCategory,
    getFileDateInfo,
    createDestinationPath,
    ensureDirectoryExists,
    getAllFiles,
    getDirectoryStats,
    generateUniqueFilename,
    formatFileSize,
    calculateMD5,
    findDuplicatesByHash,
    buildDestinationHashIndex,
    clearDestinationHashIndex,
    createLogEntry,
    FILE_TYPES
};
