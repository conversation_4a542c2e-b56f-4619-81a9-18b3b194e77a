const fs = require('fs').promises;
const path = require('path');

/**
 * Test setup script to create sample files for testing the File Organizer & Verifier
 */

async function createTestFiles() {
    const testDir = path.join(__dirname, 'test-files');

    try {
        // Create test directory
        await fs.mkdir(testDir, { recursive: true });

        // Create subdirectories
        const subDirs = ['photos', 'videos', 'documents', 'music', 'misc'];
        for (const dir of subDirs) {
            await fs.mkdir(path.join(testDir, dir), { recursive: true });
        }

        // Sample file contents
        const sampleContent = {
            image: 'Sample image file content',
            video: 'Sample video file content',
            document: 'Sample document content with some text',
            other: 'Sample file content for other types'
        };

        // Create sample files
        const testFiles = [
            // Images
            { path: 'photos/vacation.jpg', content: sampleContent.image },
            { path: 'photos/family.png', content: sampleContent.image },
            { path: 'photos/sunset.gif', content: sampleContent.image },
            { path: 'landscape.bmp', content: sampleContent.image },

            // Videos
            { path: 'videos/birthday.mp4', content: sampleContent.video },
            { path: 'videos/wedding.avi', content: sampleContent.video },
            { path: 'movie.mkv', content: sampleContent.video },

            // Documents
            { path: 'documents/report.pdf', content: sampleContent.document },
            { path: 'documents/spreadsheet.xlsx', content: sampleContent.document },
            { path: 'documents/presentation.pptx', content: sampleContent.document },
            { path: 'readme.txt', content: sampleContent.document },

            // Audio (NEW)
            { path: 'music/song.mp3', content: 'Sample MP3 audio file content' },
            { path: 'music/track.wav', content: 'Sample WAV audio file content' },
            { path: 'music/album.flac', content: 'Sample FLAC audio file content' },
            { path: 'podcast.m4a', content: 'Sample M4A audio file content' },

            // Others
            { path: 'misc/config.json', content: sampleContent.other },
            { path: 'misc/script.js', content: sampleContent.other },
            { path: 'data.xml', content: sampleContent.other },
            { path: 'archive.zip', content: sampleContent.other }
        ];

        // Write test files
        for (const file of testFiles) {
            const filePath = path.join(testDir, file.path);
            await fs.writeFile(filePath, file.content, 'utf8');
        }

        console.log('✅ Test files created successfully!');
        console.log(`📁 Test directory: ${testDir}`);
        console.log(`📊 Created ${testFiles.length} test files`);
        console.log('\nFile structure:');
        console.log('test-files/');
        console.log('├── photos/');
        console.log('│   ├── vacation.jpg');
        console.log('│   ├── family.png');
        console.log('│   └── sunset.gif');
        console.log('├── videos/');
        console.log('│   ├── birthday.mp4');
        console.log('│   └── wedding.avi');
        console.log('├── documents/');
        console.log('│   ├── report.pdf');
        console.log('│   ├── spreadsheet.xlsx');
        console.log('│   └── presentation.pptx');
        console.log('├── music/');
        console.log('│   ├── song.mp3');
        console.log('│   ├── track.wav');
        console.log('│   └── album.flac');
        console.log('├── misc/');
        console.log('│   ├── config.json');
        console.log('│   └── script.js');
        console.log('├── landscape.bmp');
        console.log('├── movie.mkv');
        console.log('├── readme.txt');
        console.log('├── podcast.m4a');
        console.log('├── data.xml');
        console.log('└── archive.zip');
        console.log('\n🚀 You can now use this folder as your Base Folder in the application!');

    } catch (error) {
        console.error('❌ Error creating test files:', error.message);
    }
}

// Run the test setup
if (require.main === module) {
    createTestFiles();
}

module.exports = { createTestFiles };
