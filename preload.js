const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Folder selection
  selectFolder: (title) => ipcRenderer.invoke('select-folder', title),

  // File operations
  copyFiles: (basePath, destPath, options) => ipcRenderer.invoke('copy-files', basePath, destPath, options),
  stopCopy: () => ipcRenderer.invoke('stop-copy'),
  verifyFiles: (basePath, destPath) => ipcRenderer.invoke('verify-files', basePath, destPath),

  // Utility functions
  getFolderStats: (folderPath) => ipcRenderer.invoke('get-folder-stats', folderPath),

  // Progress listeners
  onCopyProgress: (callback) => {
    ipcRenderer.on('copy-progress', (event, progress) => callback(progress));
  },

  onVerifyProgress: (callback) => {
    ipcRenderer.on('verify-progress', (event, progress) => callback(progress));
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Window controls
  minimize: () => ipcRenderer.invoke('minimize-window'),
  maximize: () => ipcRenderer.invoke('maximize-window'),
  fullscreen: () => ipcRenderer.invoke('fullscreen-window'),
  close: () => ipcRenderer.invoke('close-window')
});
